# API Key Funktionalität

Diese Dokumentation beschreibt die implementierte API-Key-Funktionalität für CPO-Kunden.

## Übersicht

Die API-Key-Funktionalität ermöglicht es CPO-Kunden, über API-Keys auf bestimmte API-Endpunkte zuzugreifen. API-Keys werden an CPO-Contact-Entitäten gespeichert und können von Administratoren verwaltet werden.

**Wichtig**: Nur Contacts mit `cpo: true` können API-Keys erhalten.

## Implementierte Komponenten

### 1. Datenbank-Schema
- **Contact-Model erweitert**: Neues Feld `apiKey` (String, unique, optional)
- **Migration**: `20250620074403_add_api_key_to_contact`

### 2. Utility-Funktionen
**Datei**: `src/utils/apiAuth/apiKeyUtil.ts`

#### `validateApiKey(request: NextRequest)`
- Validiert API-Keys aus dem Authorization-Header
- Unterstützt Formate: `Bearer <key>`, `ApiKey <key>` oder direkter Key
- Gibt Contact-Informationen bei gültigem Key zurück

#### `generateApiKey(prefix, length)`
- Generiert neue API-Keys mit konfigurierbarem Prefix (Standard: "EUL")
- Verwendet crypto.randomBytes für sichere Zufallsgenerierung

#### `generateUniqueApiKey(prefix, length, maxAttempts)`
- Generiert eindeutige API-Keys (prüft Datenbank auf Duplikate)
- Mehrere Versuche bei Kollisionen

### 3. API-Endpunkte

#### `/api/contact/apikey`
**POST**: API-Key für Contact generieren
```json
{
  "contactId": "string"
}
```
- Sendet Administrator-Benachrichtigung bei erfolgreicher Generierung

**DELETE**: API-Key von Contact entfernen
```json
{
  "contactId": "string"
}
```
- Sendet Administrator-Benachrichtigung bei erfolgreicher Entfernung

**GET**: Alle Contacts mit API-Key-Status abrufen
- Nur für ADMIN-Rolle zugänglich

#### `/api/test/apikey`
**GET/POST**: Test-Endpunkt für API-Key-Validierung
- Demonstriert die Verwendung der `validateApiKey`-Utility
- Gibt Contact-Informationen bei gültigem Key zurück

### 4. Admin-Verwaltungsseite
**Datei**: `src/app/(app)/contact/apikey/page.tsx`

- Übersicht aller Contacts mit API-Key-Status
- Generieren/Löschen von API-Keys
- Kopieren von API-Keys in Zwischenablage
- Anzeige von Verwendungsbeispielen

**Tabellen-Komponente**: `src/app/(app)/contact/apikey/components/ApiKeyTable.tsx`
- AgGrid-basierte Tabelle
- Aktionen: Generieren, Löschen, Kopieren
- Maskierte Anzeige der API-Keys (Sicherheit)

### 5. Self-Service-Seite für CPO/Manager
**Datei**: `src/app/(app)/my-api-key/page.tsx`

- Eigenen API-Key einsehen und verwalten
- Generieren/Regenerieren des API-Keys für die eigene OU
- Nur für CPO und CARD_MANAGER Rollen zugänglich
- Automatische OU-basierte Contact-Zuordnung

**API-Endpunkt**: `/api/my-api-key`
- GET: Eigenen API-Key abrufen
- POST: API-Key generieren/regenerieren

**Komponente**: `src/app/(app)/my-api-key/components/MyApiKeyManager.tsx`
- Self-Service API-Key-Management
- Anzeigen/Verbergen des vollständigen Keys
- Kopieren in Zwischenablage

### 6. Navigation
- **"API Key Verwaltung"** unter "Data" - Nur für ADMIN-Rolle (🔑)
- **"Mein API Key"** unter "Data" - Für CPO und CARD_MANAGER Rollen (🔐)

### 7. Contact-Detail-Integration
- API-Key-Status in Contact-Detailseite angezeigt
- Link zur API-Key-Verwaltung
- Maskierte Anzeige des Keys

## Verwendung

### Für Entwickler

#### API-Key-Validierung in Endpunkten
```typescript
import { validateApiKey } from "~/utils/apiAuth/apiKeyUtil";

export async function GET(request: NextRequest) {
  // API-Key validieren
  const validation = await validateApiKey(request);
  
  if (!validation.isValid) {
    return NextResponse.json(
      { error: validation.error },
      { status: 401 }
    );
  }
  
  // validation.contact enthält Contact-Informationen
  const contact = validation.contact;
  
  // Ihre API-Logik hier...
}
```

### Für Kunden

#### Authorization Header
API-Keys müssen im Authorization-Header gesendet werden:

```bash
# Option 1: Bearer Token
curl -H "Authorization: Bearer EUL_your_api_key_here" \
     https://your-domain.com/api/your-endpoint

# Option 2: ApiKey Format
curl -H "Authorization: ApiKey EUL_your_api_key_here" \
     https://your-domain.com/api/your-endpoint

# Option 3: Direkter Key
curl -H "Authorization: EUL_your_api_key_here" \
     https://your-domain.com/api/your-endpoint
```

#### Test-Endpunkt
```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://your-domain.com/api/test/apikey
```

## Sicherheitsaspekte

1. **Eindeutigkeit**: API-Keys sind in der Datenbank unique
2. **Maskierung**: Keys werden in der UI nur teilweise angezeigt
3. **Sichere Generierung**: Verwendung von crypto.randomBytes
4. **Autorisierung**: Nur ADMIN-Rolle kann Keys verwalten
5. **CPO-Beschränkung**: Nur CPO-Contacts können API-Keys erhalten
6. **Validierung**: Umfassende Eingabevalidierung mit Zod
7. **Audit-Trail**: Administrator-Benachrichtigungen bei Generierung/Entfernung

## API-Key-Format

- **Prefix**: "EUL_" (konfigurierbar)
- **Länge**: 32 Zeichen Zufallsteil (konfigurierbar)
- **Beispiel**: `EUL_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6`

## Fehlerbehandlung

- **401 Unauthorized**: Ungültiger oder fehlender API-Key
- **404 Not Found**: Contact nicht gefunden
- **500 Internal Server Error**: Serverfehler bei Validierung

## Administrator-Benachrichtigungen

Das System sendet automatisch Benachrichtigungen an alle Administrator-Benutzer bei:

### API-Key-Generierung
- **Nachricht**: "🔑 Neuer API Key generiert für CPO-Contact [Name] (ID: [ID])"
- **Typ**: INFO
- **Empfänger**: Alle Benutzer mit ADMIN-Rolle

### API-Key-Entfernung
- **Nachricht**: "🗑️ API Key entfernt für CPO-Contact [Name] (ID: [ID])"
- **Typ**: WARNING
- **Empfänger**: Alle Benutzer mit ADMIN-Rolle

## Zukünftige Erweiterungen

- Rate Limiting pro API-Key
- API-Key-Ablaufzeiten
- Scopes/Berechtigungen pro Key
- Erweiterte Audit-Logging für API-Key-Verwendung
- API-Key-Rotation
