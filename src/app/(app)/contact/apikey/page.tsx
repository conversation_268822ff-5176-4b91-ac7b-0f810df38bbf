import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { redirect } from "next/navigation";
import Card from "~/component/card";
import Headline from "~/component/Headline";
import { ApiKeyTable } from "./components/ApiKeyTable";
import prisma from "~/server/db/prisma";

export const revalidate = 0;

const getContactsWithApiKeys = async () => {
  try {
    const contacts = await prisma.contact.findMany({
      select: {
        id: true,
        name: true,
        companyName: true,
        apiKey: true,
        cpo: true,
        ou: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    return contacts;
  } catch (error) {
    console.error("Error fetching contacts:", error);
    // Fallback: Fetch without apiKey field if it's not available yet
    const contacts = await prisma.contact.findMany({
      select: {
        id: true,
        name: true,
        companyName: true,
        cpo: true,
        ou: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    // Add apiKey as null for all contacts
    return contacts.map((contact) => ({
      ...contact,
      apiKey: null,
    }));
  }
};

export default async function ApiKeyManagementPage() {
  const session = await getServerSession(authOptions);

  // Check if user is authenticated and has admin role
  if (!session || session?.user?.role !== Role.ADMIN) {
    redirect("/login");
  }

  const contacts = await getContactsWithApiKeys();

  return (
    <>
      <Headline title={"API Key Verwaltung"} />
      <div className={"flex flex-col gap-5"}>
        <Card header_left={"API Keys für CPO-Kunden verwalten"}>
          <div className="mb-4">
            <p className="text-sm text-gray-600">
              Hier können Sie API Keys für CPO-Kunden generieren und verwalten.
              API Keys ermöglichen es CPO-Kunden, auf bestimmte API-Endpunkte zuzugreifen.
              <strong className="text-primary"> Nur CPO-Contacts können API Keys erhalten.</strong>
            </p>
          </div>
          <ApiKeyTable contacts={contacts} />

          <div className="space-y-4">
            <div>
              <h4 className="mb-2 font-semibold text-primary">Test-Endpunkt</h4>
              <p className="mb-2 text-sm text-gray-600">
                Verwenden Sie den folgenden Endpunkt zum Testen der API Key Funktionalität:
              </p>
              <code className="rounded bg-gray-100 px-2 py-1 text-sm">GET /api/test/apikey</code>
            </div>

            <div>
              <h4 className="mb-2 font-semibold text-primary">Authorization Header</h4>
              <p className="mb-2 text-sm text-gray-600">
                Der API Key muss im Authorization Header gesendet werden:
              </p>
              <div className="font-mono rounded bg-gray-100 p-3 text-sm">
                <div>Authorization: Bearer YOUR_API_KEY</div>
                <div className="text-gray-500">oder</div>
                <div>Authorization: ApiKey YOUR_API_KEY</div>
              </div>
            </div>

            <div>
              <h4 className="mb-2 font-semibold text-primary">Beispiel cURL</h4>
              <div className="font-mono rounded bg-gray-100 p-3 text-sm">
                curl -H "Authorization: Bearer YOUR_API_KEY" /api/test/apikey
              </div>
            </div>
          </div>
        </Card>
      </div>
    </>
  );
}
