import React from "react";

import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { menuDef, MenuItem, MenuType, CollapsibleMenuSection } from "~/utils/menu/menuDef";
import MenuEntry from "~/component/MenuEntry";
import { Role } from "@prisma/client";

// Hilfsfunktion zum Filtern der Menüeinträge in einklappbaren Bereichen
const filterCollapsibleSectionItems = (section: CollapsibleMenuSection, userRole: Role): CollapsibleMenuSection => {
  const filteredItems = section.items.filter(item => {
    if (item.type === MenuType.collapsibleSection) {
      const subSection = item as CollapsibleMenuSection;
      const filteredSubSection = filterCollapsibleSectionItems(subSection, userRole);
      return filteredSubSection.items.length > 0;
    }
    return item.role.includes(userRole);
  }).map(item => {
    if (item.type === MenuType.collapsibleSection) {
      return filterCollapsibleSectionItems(item as CollapsibleMenuSection, userRole);
    }
    return item;
  });

  return {
    ...section,
    items: filteredItems
  };
};

const Menu = async () => {
  const session = await getServerSession(authOptions);

  const role = session?.user?.role;

  if (!session) {
    return <></>;
  }

  if (!role) {
    return <></>;
  }

  const visibleMenuItems = menuDef.filter(
    (menuItem) => session?.user?.role && menuItem.role.includes(session.user.role),
  ).map(menuItem => {
    // Für einklappbare Bereiche müssen wir die Unterelemente filtern
    if (menuItem.type === MenuType.collapsibleSection) {
      const section = menuItem as CollapsibleMenuSection;
      return filterCollapsibleSectionItems(section, role);
    }
    return menuItem;
  }).filter(menuItem => {
    // Einklappbare Bereiche nur anzeigen, wenn sie sichtbare Unterelemente haben
    if (menuItem.type === MenuType.collapsibleSection) {
      const section = menuItem as CollapsibleMenuSection;
      return section.items.length > 0;
    }
    return true;
  });

  return (
    <>
      <ul>
        {visibleMenuItems.map((menuItem) => {
          return (
            <MenuEntry
              key={menuItem.name}
              menuItem={menuItem}
              visibleMenuItems={visibleMenuItems}
            />
          );
        })}
      </ul>
    </>
  );
};

export default Menu;
