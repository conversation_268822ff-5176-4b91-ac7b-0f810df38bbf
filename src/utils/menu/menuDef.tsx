import React from "react";
import { Role } from "@prisma/client";

export enum MenuType {
  divider,
  menu,
  collapsibleSection,
}

export interface MenuEntry {
  name: string;
  type: MenuType.menu;
  href: string;
  subMenu?: MenuEntry[];
  icon: string;
  role: Role[];
}

export interface MenuDivider {
  name: string;
  type: MenuType.divider;
  role: Role[];
  href?: string;
}

export interface CollapsibleMenuSection {
  name: string;
  type: MenuType.collapsibleSection;
  role: Role[];
  items: MenuItem[];
  defaultCollapsed?: boolean;
  icon?: string;
}

export type MenuItem = MenuEntry | MenuDivider | CollapsibleMenuSection;

export const menuDef: MenuItem[] = [
  {
    name: "Operations",
    type: MenuType.divider,
    role: [Role.USER, Role.ADMIN, Role.CPO],
  },
  {
    name: "Dashboard",
    type: MenuType.menu,
    href: "/",
    icon: "🎛️",
    role: [Role.USER, Role.ADMIN, Role.CARD_MANAGER, Role.CPO],
  },

  {
    name: "Power Graph",
    type: MenuType.menu,
    href: "/power-visualization",
    icon: "📊",
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "Parking",
    type: MenuType.menu,
    href: "/parkingsensor",
    icon: "🅿️",
    role: [Role.ADMIN],
  },
  {
    name: "Financial",
    type: MenuType.divider,
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "Finance Dashboard",
    href: "/finance-dashboard",
    type: MenuType.menu,
    icon: "💰",
    role: [Role.ADMIN],
  },
  {
    name: "CPO Revenue Dashboard",
    href: "/cpo-revenue-dashboard",
    type: MenuType.menu,
    icon: "📊",
    role: [Role.ADMIN],
  },
  {
    name: "Forecast",
    href: "/financal-forecast",
    type: MenuType.menu,
    icon: "📈",
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "Invoice & Credit",
    href: "/invoice",
    type: MenuType.menu,
    icon: "🧾",
    role: [Role.ADMIN],
  },
  {
    name: "Invoice forecast",
    href: "/invoice-forecast",
    type: MenuType.menu,
    icon: "🕐",
    role: [Role.ADMIN],
  },
  {
    name: "Qonto Transactions",
    href: "/qonto-transactions",
    type: MenuType.menu,
    icon: "🏦",
    role: [Role.ADMIN],
  },
  {
    name: "Stripe",
    href: "/stripe",
    type: MenuType.menu,
    icon: "💳",
    role: [Role.ADMIN],
  },
  {
    name: "CPO Verträge",
    href: "/cpoContract",
    type: MenuType.menu,
    icon: "📄",
    role: [Role.ADMIN],
  },

  {
    name: "Data",
    type: MenuType.divider,
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "CDRs",
    href: "/cdr",
    type: MenuType.menu,
    icon: "🗄️",
    role: [Role.ADMIN],
  },
  {
    name: "EMP & CPO",
    href: "/contact",
    type: MenuType.menu,
    icon: "📇",
    role: [Role.ADMIN],
  },
  {
    name: "API Key Verwaltung",
    href: "/contact/apikey",
    type: MenuType.menu,
    icon: "🔑",
    role: [Role.ADMIN],
  },




  {
    name: "Flottenkarten (Token)",
    href: "/tokenGroup",
    type: MenuType.menu,
    icon: "🔑",
    role: [Role.ADMIN],
  },
  {
    name: "Tarife",
    href: "/tarif",
    type: MenuType.menu,
    icon: "💰",
    role: [Role.ADMIN],
  },
  {
    name: "Locations",
    type: MenuType.menu,
    href: "/location",
    icon: "🏢",
    role: [Role.ADMIN],
  },

  {
    name: "Admin",
    type: MenuType.divider,
    role: [Role.ADMIN],
  },
  {
    name: "Tenant",
    type: MenuType.menu,
    href: "/tenantconfiguration",
    icon: "🏛️",
    role: [Role.ADMIN],
  },
  {
    name: "OU Verwaltung",
    type: MenuType.menu,
    href: "/ous",
    icon: "🏢",
    role: [Role.ADMIN],
  },
  {
    name: "Benutzerverwaltung",
    type: MenuType.menu,
    href: "/users",
    icon: "👥",
    role: [Role.ADMIN],
  },

  {
    name: "Command",
    type: MenuType.menu,
    role: [Role.ADMIN],
    icon: "💻",
    href: "/command",
  },
  {
    name: "Log",
    type: MenuType.menu,
    href: "/log",
    icon: "📋",
    role: [Role.ADMIN],
  },
  {
    name: "Ladekarten-Management",
    type: MenuType.divider,
    role: [Role.ADMIN],
  },



  {
    name: "Mitarbeiter-Ladekarten",
    type: MenuType.menu,
    href: "/emp/card",
    icon: "💳",
    role: [Role.ADMIN, Role.USER],
  },


  {
    name: "Firmentarife Ou",
    type: MenuType.menu,
    href: "/emp/tarif/managerview",
    icon: "🏷️",
    role: [Role.ADMIN],
  },
  {
    name: "Ladevorgänge Ou",
    type: MenuType.menu,
    href: "/emp/charging-history/managerview",
    icon: "⚡️",
    role: [Role.ADMIN],
  },









  {
    name: "Sonstiges",
    type: MenuType.divider,
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "Benachrichtigungen",
    type: MenuType.menu,
    href: "/notifications",
    icon: "🔔",
    role: [Role.ADMIN, Role.CARD_HOLDER, Role.CARD_MANAGER, Role.CPO, Role.USER],
  },

  {
    name: "Profil",
    type: MenuType.menu,
    href: "/profile",
    icon: "👤",
    role: [Role.ADMIN, Role.CARD_HOLDER, Role.CARD_MANAGER, Role.CPO],
  },

  {
    name: "Support",
    type: MenuType.menu,
    href: "/support",
    icon: "❓",
    role: [Role.ADMIN, Role.CARD_HOLDER, Role.CARD_MANAGER, Role.CPO, Role.USER],
  },

  // Einklappbare Sektion für Admin-Benutzer mit CPO-Menüs
  {
    name: "CPO Ansicht",
    type: MenuType.collapsibleSection,
    role: [Role.ADMIN],
    defaultCollapsed: true,
    icon: "🔌",
    items: [
      {
        name: "Stromtarife",
        href: "/stromtarife",
        type: MenuType.menu,
        icon: "⚡",
        role: [Role.CPO],
      },
      {
        name: "Mein API Key",
        href: "/my-api-key",
        type: MenuType.menu,
        icon: "🔐",
        role: [Role.CPO],
      },
      {
        name: "Roaming Matrix",
        href: "/roaming-matrix",
        type: MenuType.menu,
        icon: "🌐",
        role: [Role.CPO],
      },
      {
        name: "Maintenance",
        type: MenuType.menu,
        href: "/maintenance",
        icon: "🔧",
        role: [Role.CPO],
      },
      {
        name: "Ladevorgänge",
        type: MenuType.menu,
        href: "/emp/charging-history/managerview",
        icon: "📋",
        role: [Role.CPO],
      },
    ],
  },

  // Einklappbare Sektion für Admin-Benutzer mit Card Manager-Menüs
  {
    name: "Card Manager Ansicht",
    type: MenuType.collapsibleSection,
    role: [Role.ADMIN],
    defaultCollapsed: true,
    icon: "💳",
    items: [
      {
        name: "Stromtarife",
        href: "/stromtarife",
        type: MenuType.menu,
        icon: "⚡",
        role: [Role.CARD_MANAGER],
      },
      {
        name: "Mein API Key",
        href: "/my-api-key",
        type: MenuType.menu,
        icon: "🔐",
        role: [Role.CARD_MANAGER],
      },
      {
        name: "Roaming Matrix",
        href: "/roaming-matrix",
        type: MenuType.menu,
        icon: "🌐",
        role: [Role.CARD_MANAGER],
      },
      {
        name: "Nutzergruppen",
        type: MenuType.menu,
        href: "/userGroups",
        icon: "👥",
        role: [Role.CARD_MANAGER],
      },
      {
        name: "Ladekarten",
        type: MenuType.menu,
        href: "/emp/card",
        icon: "💳",
        role: [Role.CARD_MANAGER],
      },
      {
        name: "Firmentarife",
        type: MenuType.menu,
        href: "/emp/tarif/managerview",
        icon: "🏷️",
        role: [Role.CARD_MANAGER],
      },
      {
        name: "Ladevorgänge",
        type: MenuType.menu,
        href: "/emp/charging-history/managerview",
        icon: "📋",
        role: [Role.CARD_MANAGER],
      },
      {
        name: "Rechnungen",
        type: MenuType.menu,
        href: "/emp/invoice/managerview",
        icon: "🧾",
        role: [Role.CARD_MANAGER],
      },
      {
        name: "Benutzerverwaltung",
        type: MenuType.menu,
        href: "/users",
        icon: "👥",
        role: [Role.CARD_MANAGER],
      },
    ],
  },

  // Einklappbare Sektion für Admin-Benutzer mit Card Holder-Menüs
  {
    name: "Card Holder Ansicht",
    type: MenuType.collapsibleSection,
    role: [Role.ADMIN],
    defaultCollapsed: true,
    icon: "👤",
    items: [
      {
        name: "Home",
        type: MenuType.menu,
        href: "/",
        icon: "🏠",
        role: [Role.CARD_HOLDER],
      },
      {
        name: "Zahlungsdaten",
        type: MenuType.menu,
        href: "/emp/payment",
        icon: "💰",
        role: [Role.CARD_HOLDER],
      },
      {
        name: "Ladekarten",
        type: MenuType.menu,
        href: "/emp/card",
        icon: "💳",
        role: [Role.CARD_HOLDER],
      },
      {
        name: "Mein Tarif",
        type: MenuType.menu,
        href: "/emp/tarif/userview",
        icon: "🏷️",
        role: [Role.CARD_HOLDER],
      },
      {
        name: "Ladevorgänge",
        type: MenuType.menu,
        href: "/emp/charging-history/userview",
        icon: "⚡️",
        role: [Role.CARD_HOLDER],
      },
      {
        name: "Rechnungen",
        type: MenuType.menu,
        href: "/emp/invoice/userview",
        icon: "🧾",
        role: [Role.CARD_HOLDER],
      },
    ],
  },
];
