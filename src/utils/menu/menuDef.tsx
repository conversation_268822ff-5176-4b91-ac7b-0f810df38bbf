import React from "react";
import { Role } from "@prisma/client";

export enum MenuType {
  divider,
  menu,
  collapsibleSection,
}

export interface MenuEntry {
  name: string;
  type: MenuType.menu;
  href: string;
  subMenu?: MenuEntry[];
  icon: string;
}

export interface MenuDivider {
  name: string;
  type: MenuType.divider;
  href?: string;
}

export interface CollapsibleMenuSection {
  name: string;
  type: MenuType.collapsibleSection;
  items: MenuItem[];
  defaultCollapsed?: boolean;
  icon?: string;
}

export type MenuItem = MenuEntry | MenuDivider | CollapsibleMenuSection;

export interface RoleMenuDefinition {
  [Role.ADMIN]: MenuItem[];
  [Role.CPO]: MenuItem[];
  [Role.CARD_MANAGER]: MenuItem[];
  [Role.CARD_HOLDER]: MenuItem[];
  [Role.USER]: MenuItem[];
}

// Admin Menu Definition
const adminMenuDef: MenuItem[] = [
  {
    name: "Operations",
    type: MenuType.divider,
  },
  {
    name: "Dashboard",
    type: MenuType.menu,
    href: "/",
    icon: "🎛️",
  },
  {
    name: "Power Graph",
    type: MenuType.menu,
    href: "/power-visualization",
    icon: "📊",
  },
  {
    name: "Parking",
    type: MenuType.menu,
    href: "/parkingsensor",
    icon: "🅿️",
  },
  {
    name: "Financial",
    type: MenuType.divider,
  },
  {
    name: "Finance Dashboard",
    href: "/finance-dashboard",
    type: MenuType.menu,
    icon: "💰",
  },
  {
    name: "CPO Revenue Dashboard",
    href: "/cpo-revenue-dashboard",
    type: MenuType.menu,
    icon: "📊",
  },
  {
    name: "Forecast",
    href: "/financal-forecast",
    type: MenuType.menu,
    icon: "📈",
  },
  {
    name: "Invoice & Credit",
    href: "/invoice",
    type: MenuType.menu,
    icon: "🧾",
  },
  {
    name: "Invoice forecast",
    href: "/invoice-forecast",
    type: MenuType.menu,
    icon: "🕐",
  },
  {
    name: "Qonto Transactions",
    href: "/qonto-transactions",
    type: MenuType.menu,
    icon: "🏦",
  },
  {
    name: "Stripe",
    href: "/stripe",
    type: MenuType.menu,
    icon: "💳",
  },
  {
    name: "CPO Verträge",
    href: "/cpoContract",
    type: MenuType.menu,
    icon: "📄",
  },
  {
    name: "Data",
    type: MenuType.divider,
  },
  {
    name: "CDRs",
    href: "/cdr",
    type: MenuType.menu,
    icon: "🗄️",
  },
  {
    name: "EMP & CPO",
    href: "/contact",
    type: MenuType.menu,
    icon: "📇",
  },
  {
    name: "API Key Verwaltung",
    href: "/contact/apikey",
    type: MenuType.menu,
    icon: "🔑",
  },
  {
    name: "Mein API Key (Ou)",
    href: "/my-api-key",
    type: MenuType.menu,
    icon: "🔐",
  },
  {
    name: "Flottenkarten (Token)",
    href: "/tokenGroup",
    type: MenuType.menu,
    icon: "🔑",
  },
  {
    name: "Tarife",
    href: "/tarif",
    type: MenuType.menu,
    icon: "💰",
  },
  {
    name: "Locations",
    type: MenuType.menu,
    href: "/location",
    icon: "🏢",
  },
  {
    name: "Admin",
    type: MenuType.divider,
  },
  {
    name: "Tenant",
    type: MenuType.menu,
    href: "/tenantconfiguration",
    icon: "🏛️",
  },
  {
    name: "OU Verwaltung",
    type: MenuType.menu,
    href: "/ous",
    icon: "🏢",
  },
  {
    name: "Benutzerverwaltung",
    type: MenuType.menu,
    href: "/users",
    icon: "👥",
  },
  {
    name: "Command",
    type: MenuType.menu,
    icon: "💻",
    href: "/command",
  },
  {
    name: "Log",
    type: MenuType.menu,
    href: "/log",
    icon: "📋",
  },
  {
    name: "Ladekarten-Management",
    type: MenuType.divider,
  },
  {
    name: "Mitarbeiter-Ladekarten",
    type: MenuType.menu,
    href: "/emp/card",
    icon: "💳",
  },
  {
    name: "Firmentarife Ou",
    type: MenuType.menu,
    href: "/emp/tarif/managerview",
    icon: "🏷️",
  },
  {
    name: "Ladevorgänge Ou",
    type: MenuType.menu,
    href: "/emp/charging-history/managerview",
    icon: "⚡️",
  },
  {
    name: "Sonstiges",
    type: MenuType.divider,
  },
  {
    name: "Benachrichtigungen",
    type: MenuType.menu,
    href: "/notifications",
    icon: "🔔",
  },
  {
    name: "Profil",
    type: MenuType.menu,
    href: "/profile",
    icon: "👤",
  },
  {
    name: "Support",
    type: MenuType.menu,
    href: "/support",
    icon: "❓",
  },

  // Einklappbare Sektionen für andere Rollen-Ansichten
  {
    name: "CPO Ansicht",
    type: MenuType.collapsibleSection,
    defaultCollapsed: true,
    icon: "🔌",
    items: [
      {
        name: "Stromtarife",
        href: "/stromtarife",
        type: MenuType.menu,
        icon: "⚡",
      },
      {
        name: "Mein API Key",
        href: "/my-api-key",
        type: MenuType.menu,
        icon: "🔐",
      },
      {
        name: "Roaming Matrix",
        href: "/roaming-matrix",
        type: MenuType.menu,
        icon: "🌐",
      },
      {
        name: "Maintenance",
        type: MenuType.menu,
        href: "/maintenance",
        icon: "🔧",
      },
      {
        name: "Ladevorgänge",
        type: MenuType.menu,
        href: "/emp/charging-history/managerview",
        icon: "📋",
      },
    ],
  },
];
